幻境游戏
职位描述
1.使用React开发高响应性的用户界面组件
2.构建和管理REST API，实现数据库与网页端
3.整合第三方API
4.维护产品的稳定性和安全性，对用户反馈的问题进行修复和优化

职位描述
1.使用React开发高响应性的用户界面组件
2.构建和管理REST API，实现数据库与网页端
3.整合第三方API
4.维护产品的稳定性和安全性，对用户反馈的问题进行修复和优化
职位要求
1.以JavaScript 为基础的前端开发能力，包括 HTML5、C
SS、前端 JS、React的开发能力
2.对Node.js,Express等有基础使用能
3.熟悉Next.js 、Typescript
4.三年以上web 前端和 Node后端开发经验
5.对于产品和 UX 有审美能力
6.具有扎实的编程功底，良好的设计能力和编程习惯;有优秀的优化、调试和解决问题能力，逻辑思维清晰严谨
7.有强烈的求知欲和进取心;自良好的沟通能力和团队协作精
神，有创业公司经验者优先
8.良好的英文文档阅读能力
9.有CICD经验，docker优先
10.名校和硕士学位以上优先