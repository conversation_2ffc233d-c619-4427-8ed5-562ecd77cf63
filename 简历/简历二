个人简历 - 岗位一优化版本1.1（幻境游戏）

个人信息
姓名：吴先生 性别：男
学校：兰州大学 学历：硕士 工作年限：10+
联系电话：13049380563 电子邮件：<EMAIL> 期望薪资：面议
求职意向：全栈开发工程师（React/Node.js） 求职状态：已离职
教育背景
2021.09 — 2024.06 兰州大学 MBA
2009.09 — 2012.06 湖南信息学院 计算机科学与技术
个人技能
1. 精通 HTML/CSS 布局，有良好的代码书写规范，符合 W3C 标准、兼容主流浏览器，并熟练使
用 Less/Sass 预处理器；
2. 精通 ElementUI、iView、Vant、Ant Design 等 UI 框架，能快速高效实现⻚面的开发；
3. 精通 Vue2、Vue3、React 框架，以及使用 Vue-Router、Vuex、Pinia、React Router、
Redux 等全家桶；
4. 熟练使用 JavaScript、TypeScript，熟练掌握 DOM 操作，熟练运用 ES6、ES7等语法；
5. 熟练使用 Echarts 图表绘制、AntV-G6图谱、AntV-X6流程图 等可视化库；
6. 熟练使用 webpack、Vite 打包工具，git 代码管理工具；
7. 熟练使用 Ajax、Promise、axios 库实现与后台的数据交互和异步更新数据；
8. 熟练使用 qiankun 微前端架构，实现大型项目的模块化管理；
9. 熟悉 Node.js、Express 服务端开发，SSR 服务端渲染技术；
10. 熟悉 微信小程序开发、移动端 H5 开发、；
11. 熟悉 http 协议、WebSocket 实时通讯、性能优化、错误监控等；
工作经历
公司名称：深圳鑫网泰科技有限公司
岗位职责： 高级前端开发工程师
工作时间： 2024/03 – 2025/04
工作描述： 负责公司核心业务系统的前端开发工作，完成复杂功能模块的设计与实现，参与技术方
案制定和代码优化工作
项目经历
项目经验一 2024/03 – 2025/04 shopify独立站

项目介绍：
基于Shopify平台开发的跨境电商独立站项目，主要面向欧美市场销售消费产品。项目包含商品展示、购物车、支付结算、用户中心、订单管理等完整电商功能模块，支持多语言、多货币，并集成了多种第三方支付方式。

负责模块：
- 商品详情页面的交互设计与开发，包括商品图片轮播、规格选择、库存显示等
- 购物车功能模块，实现商品添加、删除、数量修改、优惠券应用等功能
- 用户注册登录系统，支持邮箱注册、社交媒体登录、忘记密码等功能
- 响应式设计适配，确保在PC、平板、手机端的良好用户体验
- 网站性能优化

工作亮点：
- 通过优化商品详情页的加载速度，将页面首屏渲染时间从3.2秒优化到1.8秒，提升了35%的页面转化率
- 实现了智能商品推荐功能，基于用户浏览历史和购买记录

使用技术栈：
- 前端框架：React.js、Next.js
- 构建工具：Webpack
- 代码规范：ESLint、Prettier
- 代码质量：ESLint、Husky、Commitizen
- 版本控制：Git、GitHub
- 第三方集成：Shopify API、Stripe支付、Google Analytics
项目经验二 2024/03 – 2025/04 电商erp管理系统

项目介绍：
为跨境电商企业开发的一体化ERP管理系统，涵盖商品管理、订单处理、库存管理、财务结算、数据分析等核心业务模块。系统支持多平台订单同步（Amazon、Shopify等），实现了从采购到发货的全流程数字化管理。

负责模块：
- 商品管理系统前端开发，包括商品录入、批量导入、SKU管理、价格策略设置等功能
- 订单管理模块，实现订单列表、订单详情、批量处理、物流跟踪等功能
- 数据可视化看板开发，展示销售趋势、库存预警、利润分析等关键业务指标
- 权限管理系统，支持多角色权限分配、部门管理、操作日志记录
- 系统消息通知模块，包括站内消息、邮件提醒、短信通知等功能

工作亮点：
- 设计并实现了高效的数据表格组件，支持虚拟滚动技术，能够流畅展示10万+条数据记录，解决了大数据量渲染的性能问题
- 开发了批量操作功能，支持订单批量处理、商品批量修改等操作，提高运营人员的工作效率
- 实现了实时数据同步机制，通过WebSocket技术确保多用户协作时数据的一致性，减少了数据冲突问题
- 构建了可复用的图表组件库，基于ECharts封装了20+种业务图表，为数据分析提供了丰富的可视化展示
- 优化系统架构，采用微前端设计模式，将系统拆分为多个独立模块，提升了系统的可维护性和扩展性

使用技术栈：
- 前端框架：Vue.js 3、Vue Router、Pinia
- UI组件库：Ant Design Vue
- 构建工具：Vite、Rollup
- 开发语言：TypeScript、JavaScript
- 样式预处理：Less、PostCSS
- 接口请求：Axios、GraphQL
- 实时通信：Socket.io、WebSocket
- 代码规范：ESLint、Prettier
- 代码质量：ESLint、Husky、Commitizen
- 版本控制：Git、GitLab CI/CD
工作经历二
公司名称：湖南兴盛优选网络科技有限公司
岗位职责： 高级前端开发工程师
工作时间： 2021/05 – 2023/12
工作描述： 负责电商平台和供应链系统的前端开发，完成多个核心业务模块的开发与维护，参与系
统架构优化和性能提升
项目经历
项目经验一 2021/05 – 2023/12 兴盛优选电商平台系统

项目介绍：兴盛优选核心电商业务平台，涵盖商品管理、订单处理、用户管理、支付系统等电商核心功能，支撑千万级用户的日常购物需求，包括PC端管理后台和移动端用户界面。

负责模块：商品管理模块、订单管理、用户中心、数据统计大屏

工作亮点：
1. 基于Vue3 + TypeScript实现商品管理系统，支持商品上架下架、库存管理、价格策略等功能，通过Element Plus组件库快速构建管理界面
2. 基于微信小程序技术栈开发移动端用户界面，实现商品浏览、购物车、下单支付等核心购物流程
3. 使用Echarts实现数据可视化大屏，展示销售数据、用户行为分析等关键业务指标
4. 集成微信支付、支付宝等第三方支付接口，实现订单支付流程，并处理支付回调和异常情况

使用技术：Vue3、TypeScript、微信小程序、Echarts、Vite、Pinia
项目经验二 2022/01 – 2023/12 兴盛优选供应链物流管理系统

项目介绍：支撑兴盛优选全国仓储物流业务的管理系统，实现从采购入库到配送出库的全流程管理，包括仓库管理、运输调度、配送跟踪等核心功能，日均处理订单10万+，覆盖全国30+个仓储中心。

负责模块：仓库管理模块、配送管理、运输调度、数据报表

工作亮点：
- 基于qiankun微前端架构实现多个子系统的统一管理，包括入库管理、出库管理、库存管理等独立模块
- 使用Vue3 + Vant开发移动端PDA操作界面，实现扫码入库、拣货、配送等仓储作业流程，提升仓库操作效率
- 集成百度地图API实现配送路径规划和实时位置追踪，为用户提供准确的配送信息
- 基于WebSocket实现实时消息推送，包括订单状态更新、异常告警等，确保业务流程及时响应

使用技术：Vue3、qiankun、element-ui、百度地图、WebSocket、Echarts
