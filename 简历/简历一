吴喜才
电话：15796433831 邮箱：<EMAIL>

工作经历

深圳鑫网泰科技有限公司 - 前端leader (2024年3月-2025年04月)
- 组建并领导前端开发团队，制定技术规划和开发流程，确保项目高质量按时交付
- 主导前端技术架构设计和技术栈选型，推动关键技术决策，解决复杂技术难题
- 设计并实现核心业务模块，优化前端性能，提升应用响应速度和用户体验
- 与产品、设计和后端团队紧密协作，确保产品需求准确实现和技术方案落地

兴盛优选 - 前端开发小组负责人 (2021年5月-2023年12月)
- 领导前端团队进行多个核心业务项目的开发，制定技术方案和开发计划
- 主导前端架构升级和技术栈优化，提升开发效率和应用性能
- 设计并实现复杂功能模块，解决技术难点，确保产品质量和用户体验
- 建立前端开发规范和代码审查机制，提高代码质量和可维护性

货拉拉 - 高级前端工程师 (2018年9月-2020年11月)
- 负责核心业务模块的技术方案设计和开发实现，解决关键技术难题
- 构建基于Sentry的前端监控体系，设计错误追踪与告警机制，提升问题定位效率
- 参与前端架构优化和性能提升工作，改善用户体验和页面加载速度
- 指导初级工程师，组织技术分享，提升团队整体技术水平

酷狗音乐 - 前端工程师 (2015年5月-2018年5月)
- 负责音乐平台核心功能模块的设计与开发，确保产品功能稳定和用户体验
- 设计并实现BFF中间层服务，优化首屏加载性能和服务端渲染
- 协助解决跨平台兼容性问题，确保应用在不同设备上的一致体验

广州软通动力有限公司 - 前端工程师 (2013年9月-2015年3月)
广州康爱多医药连锁有限公司 - 前端工程师 (2012年6月-2013年8月)

核心项目
项目一：兴盛优选电商平台
技术栈：微信小程序
角色：前端项目负责人
关键职责：
架构设计与优化：基于微信小程序技术栈，设计可扩展、高性能的前端架构
团队管理与协作：负责前端团队人员分工、任务分配与进度把控，组织技术分享与代码review，提升团队整体开发能力与代码质量

项目二：兴盛优选供应链物流系统
技术栈：Vue2/3 + ES6/TypeScript + qiankun
角色：前端负责人+项目管理
价值：支撑日均10万+仓储操作，降低30%人工调度成本
关键职责：业务需求梳理，项目架构搭建+核心模块开发
业务价值：支撑全国30+仓库统一管理。全程可视化追踪（从入库到签收）PDA移动端操作替代纸质单据，仓库人员作业效率

项目三：兴盛优选OA系统
技术栈：Vue3 + qiankun
角色：前端负责人+项目管理
关键职责：业务需求梳理，项目架构搭建+核心模块开发
业务价值：支撑2万+员工，可用性99.99%
关键技术：
微前端qiankun架构落地10+子系统
UI组件库建设（50+组件）

项目四：货拉拉客服中心系统
技术栈：Vue2 + iView
角色：前端负责人
关键职责：业务需求梳理，项目架构搭建+核心模块开发
业务价值：支持日均处理30万+客服工单，系统可用性达99.95%，实时监控大屏帮助管理端决策效率提升40%

项目五：货拉拉前端监控系统
技术栈：Sentry开源项目
角色：项目负责人
价值：帮助开发人员快速定位线上问题。错误捕获率98%，故障定位15分钟内

项目六：酷狗/HiFi客户端web页面
技术栈：Hybrid（C++ web）
角色：前端开发工程师
关键职责：业务需求梳理，核心模块开发
业务价值：首款Hi-Res认证Web音乐应用

项目七：Node.js SSR官网
技术栈：Node.js + express + Redis
角色：项目负责人+架构设计+核心开发
关键职责：业务需求梳理，核心模块开发
业务价值：提升接口性能，提升系统稳定性，降低服务器成本，降低开发维护成本
技术栈与技能

核心技术
- 前端框架：React, Vue, ES6/TypeScript
- 工程化工具：Webpack, Vite, ESLint, jenkins
- 性能优化：性能监控、缓存策略、资源加载优化
- 架构能力：微前端、中台技术、DevOps实践

教育背景
- MBA, 兰州大学(2021/09-2024/06)
- 计算机科学与技术, 湖南信息学院(2009/09-2012/06)